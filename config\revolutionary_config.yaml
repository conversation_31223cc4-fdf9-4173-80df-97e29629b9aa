# 革命性AI运维管理平台配置文件
# Revolutionary AI Operations Management Platform Configuration

# 应用基础配置
app:
  name: "revolutionary-aiops-platform"
  env: "development"
  debug: true
  port: 8080
  version: "2.0.0"

# 数据库配置
database:
  path: "./data/revolutionary_aiops.db"
  max_open_conns: 100
  max_idle_conns: 10
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"

# JWT配置
jwt:
  secret: "${AIOPS_JWT_SECRET}"
  access_token_ttl: "15m"
  refresh_token_ttl: "7d"
  issuer: "revolutionary-aiops-platform"
  max_concurrent_sessions: 10

# DeepSeek AI配置
deepseek:
  api_key: "${AIOPS_DEEPSEEK_API_KEY}"
  api_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "30s"
  max_retries: 3
  max_context_tokens: 8000
  temperature: 0.7
  top_p: 0.9

# 安全配置
security:
  encryption_key: "${AIOPS_ENCRYPTION_KEY}"
  password_hash_cost: 12
  session_timeout: "24h"
  rate_limit:
    enabled: true
    global: "2000/min"
    per_user: "200/min"
    per_ip: "500/min"

# Redis配置
redis:
  enabled: true
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 20

# SSH配置
ssh:
  timeout: "30s"
  max_connections: 50
  idle_timeout: "5m"
  health_check_interval: "1m"

# 日志配置
log:
  level: "info"
  file: "./logs/revolutionary_aiops.log"
  max_size: 100
  retention_days: 30
  format: "json"

# 监控配置
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# 缓存配置
cache:
  enabled: true
  l1_size: "500MB"
  l1_ttl: "10m"
  l2_ttl: "2h"

# Agent配置
agent:
  enabled: true
  max_agents: 100
  max_concurrent_requests: 20
  health_check_interval: "30s"
  registration_ttl: "2h"
  cleanup_interval: "10m"
  enable_auto_registration: true

# AI大脑配置
ai_brain:
  enabled: true
  name: "Revolutionary-AI-Brain"
  version: "2.0.0"
  max_concurrent_ops: 1000
  response_timeout: "30s"
  learning_enabled: true
  prediction_enabled: true
  
  # 知识图谱配置
  knowledge_graph:
    enabled: true
    max_nodes: 100000
    max_edges: 1000000
    cache_size: 10000
    persistence_enabled: true
    auto_learning_enabled: true
  
  # 专家系统配置
  expert_system:
    enabled: true
    rule_count: 1000
  
  # 决策引擎配置
  decision_engine:
    enabled: true
    confidence_threshold: 0.8
  
  # 学习引擎配置
  learning_engine:
    enabled: true
    model_type: "ensemble"
    update_freq: "1h"

# 意图识别引擎配置
intent_engine:
  enabled: true
  name: "Revolutionary-Intent-Engine"
  version: "2.0.0"
  max_concurrent_requests: 1000
  processing_timeout: "10s"
  confidence_threshold: 0.7
  multimodal_enabled: true
  context_window_size: 20
  intent_chain_max_depth: 10
  self_learning_enabled: true
  cache_enabled: true

# 多模态配置
multimodal:
  enabled: true
  
  # 文本处理器配置
  text_processor:
    max_length: 10000
    language: "zh-CN"
    normalization_enabled: true
    sentiment_analysis_enabled: true
  
  # 语音处理器配置
  voice_processor:
    sample_rate: 16000
    audio_format: "wav"
    language: "zh-CN"
    noise_reduction_enabled: true
  
  # 图像处理器配置
  image_processor:
    max_resolution: "1920x1080"
    supported_formats: ["jpg", "png", "bmp", "webp"]
    ocr_enabled: true
    object_detection_enabled: true
  
  # 手势处理器配置
  gesture_processor:
    supported_gestures: ["swipe", "tap", "pinch", "rotate"]
    sensitivity: 0.8
    tracking_points: 20

# 执行引擎配置
execution:
  enabled: true
  max_concurrent_tasks: 100
  task_timeout: "5m"
  retry_attempts: 3
  health_check_interval: "30s"
  auto_scaling_enabled: true
  load_balancing_enabled: true
  fault_tolerance_enabled: true

# 知识库配置
knowledge:
  enabled: true
  database_path: "./data/knowledge.db"
  vector_dimension: 768
  index_type: "hnsw"
  similarity_metric: "cosine"
  cache_size: 10000
  auto_update_enabled: true
