package intent_engine

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// NextGenIntentRecognizer 下一代意图识别引擎
type NextGenIntentRecognizer struct {
	// 核心组件
	multimodalProcessor *MultimodalProcessor
	contextManager      *AdvancedContextManager
	intentChainReasoner *IntentChainReasoner
	parameterExtractor  *SmartParameterExtractor
	semanticParser      *SemanticParsingEngine
	entityRecognizer    *NamedEntityRecognizer
	relationExtractor   *RelationExtractionEngine
	confidenceAssessor  *ConfidenceAssessmentEngine

	// 配置和状态
	config *IntentEngineConfig
	logger *logrus.Logger
	mutex  sync.RWMutex

	// 运行时状态
	isRunning       bool
	processedCount  int64
	accuracyRate    float64
	avgProcessTime  time.Duration
	supportedIntents map[string]*IntentDefinition
}

// IntentEngineConfig 意图引擎配置
type IntentEngineConfig struct {
	Name                    string        `json:"name"`
	Version                 string        `json:"version"`
	MaxConcurrentRequests   int           `json:"max_concurrent_requests"`
	ProcessingTimeout       time.Duration `json:"processing_timeout"`
	ConfidenceThreshold     float64       `json:"confidence_threshold"`
	MultimodalEnabled       bool          `json:"multimodal_enabled"`
	ContextWindowSize       int           `json:"context_window_size"`
	IntentChainMaxDepth     int           `json:"intent_chain_max_depth"`
	SelfLearningEnabled     bool          `json:"self_learning_enabled"`
	CacheEnabled            bool          `json:"cache_enabled"`
	
	// AI模型配置
	DeepSeekConfig          *DeepSeekConfig          `json:"deepseek_config"`
	TransformerConfig       *TransformerConfig       `json:"transformer_config"`
	EmbeddingConfig         *EmbeddingConfig         `json:"embedding_config"`
}

// IntentRecognitionRequest 意图识别请求
type IntentRecognitionRequest struct {
	ID              string                 `json:"id"`
	UserInput       string                 `json:"user_input"`
	InputType       string                 `json:"input_type"` // text, voice, image, gesture
	SessionID       string                 `json:"session_id"`
	UserID          int64                  `json:"user_id"`
	Context         *ConversationContext   `json:"context"`
	Metadata        map[string]interface{} `json:"metadata"`
	Timestamp       time.Time              `json:"timestamp"`
	RequiredConfidence float64             `json:"required_confidence"`
}

// IntentRecognitionResponse 意图识别响应
type IntentRecognitionResponse struct {
	ID                  string                 `json:"id"`
	PrimaryIntent       *RecognizedIntent      `json:"primary_intent"`
	AlternativeIntents  []*RecognizedIntent    `json:"alternative_intents"`
	IntentChain         []*IntentChainNode     `json:"intent_chain"`
	ExtractedEntities   []*ExtractedEntity     `json:"extracted_entities"`
	ExtractedRelations  []*ExtractedRelation   `json:"extracted_relations"`
	ContextUpdates      *ContextUpdates        `json:"context_updates"`
	ProcessingMetrics   *ProcessingMetrics     `json:"processing_metrics"`
	Recommendations     []*IntentRecommendation `json:"recommendations"`
	RequiredClarifications []*Clarification    `json:"required_clarifications"`
}

// RecognizedIntent 识别的意图
type RecognizedIntent struct {
	Type            string                 `json:"type"`
	Category        string                 `json:"category"`
	Subcategory     string                 `json:"subcategory"`
	Confidence      float64                `json:"confidence"`
	Parameters      map[string]interface{} `json:"parameters"`
	RequiredParams  []string               `json:"required_params"`
	OptionalParams  []string               `json:"optional_params"`
	MissingParams   []string               `json:"missing_params"`
	RiskLevel       string                 `json:"risk_level"`
	ComplexityLevel string                 `json:"complexity_level"`
	EstimatedTime   time.Duration          `json:"estimated_time"`
	Dependencies    []string               `json:"dependencies"`
}

// IntentChainNode 意图链节点
type IntentChainNode struct {
	Intent      *RecognizedIntent `json:"intent"`
	Sequence    int               `json:"sequence"`
	Condition   string            `json:"condition"`
	Probability float64           `json:"probability"`
	NextNodes   []string          `json:"next_nodes"`
}

// ExtractedEntity 提取的实体
type ExtractedEntity struct {
	Text       string                 `json:"text"`
	Type       string                 `json:"type"`
	Subtype    string                 `json:"subtype"`
	Value      interface{}            `json:"value"`
	Confidence float64                `json:"confidence"`
	Position   *TextPosition          `json:"position"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// ExtractedRelation 提取的关系
type ExtractedRelation struct {
	Subject    *ExtractedEntity `json:"subject"`
	Predicate  string           `json:"predicate"`
	Object     *ExtractedEntity `json:"object"`
	Confidence float64          `json:"confidence"`
	Context    string           `json:"context"`
}

// TextPosition 文本位置
type TextPosition struct {
	Start int `json:"start"`
	End   int `json:"end"`
}

// ContextUpdates 上下文更新
type ContextUpdates struct {
	NewVariables    map[string]interface{} `json:"new_variables"`
	UpdatedVariables map[string]interface{} `json:"updated_variables"`
	RemovedVariables []string               `json:"removed_variables"`
	IntentHistory   []*HistoricalIntent    `json:"intent_history"`
}

// HistoricalIntent 历史意图
type HistoricalIntent struct {
	Intent    *RecognizedIntent `json:"intent"`
	Timestamp time.Time         `json:"timestamp"`
	Resolved  bool              `json:"resolved"`
	Outcome   string            `json:"outcome"`
}

// ProcessingMetrics 处理指标
type ProcessingMetrics struct {
	TotalTime           time.Duration `json:"total_time"`
	MultimodalTime      time.Duration `json:"multimodal_time"`
	SemanticParsingTime time.Duration `json:"semantic_parsing_time"`
	EntityExtractionTime time.Duration `json:"entity_extraction_time"`
	IntentClassificationTime time.Duration `json:"intent_classification_time"`
	ConfidenceAssessmentTime time.Duration `json:"confidence_assessment_time"`
	ContextProcessingTime   time.Duration `json:"context_processing_time"`
}

// IntentRecommendation 意图推荐
type IntentRecommendation struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Confidence  float64 `json:"confidence"`
	Reason      string  `json:"reason"`
	Action      string  `json:"action"`
}

// Clarification 澄清请求
type Clarification struct {
	Type        string   `json:"type"`
	Question    string   `json:"question"`
	Options     []string `json:"options"`
	Required    bool     `json:"required"`
	Context     string   `json:"context"`
}

// IntentDefinition 意图定义
type IntentDefinition struct {
	Type            string                 `json:"type"`
	Category        string                 `json:"category"`
	Subcategory     string                 `json:"subcategory"`
	Description     string                 `json:"description"`
	Examples        []string               `json:"examples"`
	Keywords        []string               `json:"keywords"`
	Patterns        []string               `json:"patterns"`
	RequiredEntities []string              `json:"required_entities"`
	OptionalEntities []string              `json:"optional_entities"`
	RiskLevel       string                 `json:"risk_level"`
	ComplexityLevel string                 `json:"complexity_level"`
	Dependencies    []string               `json:"dependencies"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// 支持的50+种运维场景意图类型
var SupportedIntentTypes = map[string]*IntentDefinition{
	// 1. 主机与基础设施管理类 (10种)
	"host_management": {
		Type: "host_management", Category: "infrastructure", Subcategory: "host",
		Description: "主机管理操作，包括添加、删除、修改主机信息",
		Keywords: []string{"主机", "服务器", "host", "server", "添加", "删除", "修改"},
		RiskLevel: "medium", ComplexityLevel: "medium",
	},
	"host_monitoring": {
		Type: "host_monitoring", Category: "infrastructure", Subcategory: "monitoring",
		Description: "主机监控和状态检查",
		Keywords: []string{"监控", "状态", "检查", "monitor", "status", "health"},
		RiskLevel: "low", ComplexityLevel: "low",
	},
	"resource_optimization": {
		Type: "resource_optimization", Category: "infrastructure", Subcategory: "optimization",
		Description: "资源优化和性能调优",
		Keywords: []string{"优化", "性能", "资源", "调优", "optimization", "performance"},
		RiskLevel: "medium", ComplexityLevel: "high",
	},
	"capacity_planning": {
		Type: "capacity_planning", Category: "infrastructure", Subcategory: "planning",
		Description: "容量规划和扩容建议",
		Keywords: []string{"容量", "规划", "扩容", "capacity", "planning", "scaling"},
		RiskLevel: "low", ComplexityLevel: "high",
	},
	"hardware_management": {
		Type: "hardware_management", Category: "infrastructure", Subcategory: "hardware",
		Description: "硬件管理和维护",
		Keywords: []string{"硬件", "维护", "hardware", "maintenance", "repair"},
		RiskLevel: "high", ComplexityLevel: "high",
	},

	// 2. 系统运维操作类 (15种)
	"system_administration": {
		Type: "system_administration", Category: "system", Subcategory: "admin",
		Description: "系统管理操作",
		Keywords: []string{"系统", "管理", "配置", "system", "admin", "config"},
		RiskLevel: "high", ComplexityLevel: "medium",
	},
	"service_management": {
		Type: "service_management", Category: "system", Subcategory: "service",
		Description: "服务管理操作",
		Keywords: []string{"服务", "启动", "停止", "重启", "service", "start", "stop", "restart"},
		RiskLevel: "medium", ComplexityLevel: "medium",
	},
	"process_management": {
		Type: "process_management", Category: "system", Subcategory: "process",
		Description: "进程管理操作",
		Keywords: []string{"进程", "process", "kill", "管理", "monitor"},
		RiskLevel: "high", ComplexityLevel: "medium",
	},
	"user_management": {
		Type: "user_management", Category: "system", Subcategory: "user",
		Description: "用户和权限管理",
		Keywords: []string{"用户", "权限", "user", "permission", "role", "access"},
		RiskLevel: "high", ComplexityLevel: "medium",
	},
	"package_management": {
		Type: "package_management", Category: "system", Subcategory: "package",
		Description: "软件包管理",
		Keywords: []string{"软件包", "安装", "卸载", "package", "install", "uninstall"},
		RiskLevel: "medium", ComplexityLevel: "low",
	},

	// 3. 网络与安全类 (10种)
	"network_configuration": {
		Type: "network_configuration", Category: "network", Subcategory: "config",
		Description: "网络配置管理",
		Keywords: []string{"网络", "配置", "network", "config", "routing", "firewall"},
		RiskLevel: "high", ComplexityLevel: "high",
	},
	"security_management": {
		Type: "security_management", Category: "security", Subcategory: "management",
		Description: "安全管理操作",
		Keywords: []string{"安全", "防火墙", "security", "firewall", "encryption"},
		RiskLevel: "critical", ComplexityLevel: "high",
	},
	"vulnerability_assessment": {
		Type: "vulnerability_assessment", Category: "security", Subcategory: "assessment",
		Description: "漏洞评估和扫描",
		Keywords: []string{"漏洞", "扫描", "评估", "vulnerability", "scan", "assessment"},
		RiskLevel: "medium", ComplexityLevel: "medium",
	},

	// 4. 数据库运维类 (8种)
	"database_operations": {
		Type: "database_operations", Category: "database", Subcategory: "operations",
		Description: "数据库操作管理",
		Keywords: []string{"数据库", "database", "sql", "查询", "query", "backup"},
		RiskLevel: "high", ComplexityLevel: "medium",
	},
	"database_backup": {
		Type: "database_backup", Category: "database", Subcategory: "backup",
		Description: "数据库备份和恢复",
		Keywords: []string{"备份", "恢复", "backup", "restore", "dump"},
		RiskLevel: "medium", ComplexityLevel: "medium",
	},

	// 5. 应用部署与管理类 (12种)
	"application_deployment": {
		Type: "application_deployment", Category: "application", Subcategory: "deployment",
		Description: "应用部署管理",
		Keywords: []string{"部署", "应用", "deployment", "application", "deploy"},
		RiskLevel: "high", ComplexityLevel: "high",
	},
	"container_management": {
		Type: "container_management", Category: "application", Subcategory: "container",
		Description: "容器管理操作",
		Keywords: []string{"容器", "docker", "kubernetes", "container", "pod"},
		RiskLevel: "medium", ComplexityLevel: "medium",
	},

	// 6. 监控告警类 (8种)
	"monitoring_setup": {
		Type: "monitoring_setup", Category: "monitoring", Subcategory: "setup",
		Description: "监控系统配置",
		Keywords: []string{"监控", "告警", "monitoring", "alert", "metrics"},
		RiskLevel: "low", ComplexityLevel: "medium",
	},
	"alert_management": {
		Type: "alert_management", Category: "monitoring", Subcategory: "alert",
		Description: "告警管理操作",
		Keywords: []string{"告警", "通知", "alert", "notification", "warning"},
		RiskLevel: "low", ComplexityLevel: "low",
	},

	// 7. 故障诊断与排查类 (10种)
	"troubleshooting": {
		Type: "troubleshooting", Category: "diagnosis", Subcategory: "troubleshooting",
		Description: "故障排查和诊断",
		Keywords: []string{"故障", "排查", "诊断", "troubleshoot", "debug", "issue"},
		RiskLevel: "medium", ComplexityLevel: "high",
	},
	"log_analysis": {
		Type: "log_analysis", Category: "diagnosis", Subcategory: "logs",
		Description: "日志分析和查看",
		Keywords: []string{"日志", "分析", "log", "analysis", "查看", "view"},
		RiskLevel: "low", ComplexityLevel: "medium",
	},
	"performance_diagnosis": {
		Type: "performance_diagnosis", Category: "diagnosis", Subcategory: "performance",
		Description: "性能诊断和分析",
		Keywords: []string{"性能", "诊断", "分析", "performance", "diagnosis", "bottleneck"},
		RiskLevel: "low", ComplexityLevel: "high",
	},

	// 8. 通用对话类 (5种)
	"general_chat": {
		Type: "general_chat", Category: "general", Subcategory: "chat",
		Description: "通用对话交流",
		Keywords: []string{"你好", "帮助", "hello", "help", "thanks"},
		RiskLevel: "none", ComplexityLevel: "low",
	},
	"help_request": {
		Type: "help_request", Category: "general", Subcategory: "help",
		Description: "帮助请求",
		Keywords: []string{"帮助", "help", "如何", "how", "指导", "guide"},
		RiskLevel: "none", ComplexityLevel: "low",
	},
}

// NewNextGenIntentRecognizer 创建下一代意图识别引擎
func NewNextGenIntentRecognizer(config *IntentEngineConfig, logger *logrus.Logger) (*NextGenIntentRecognizer, error) {
	if config == nil {
		config = getDefaultIntentEngineConfig()
	}

	recognizer := &NextGenIntentRecognizer{
		config:           config,
		logger:           logger,
		isRunning:        false,
		processedCount:   0,
		accuracyRate:     0.0,
		avgProcessTime:   0,
		supportedIntents: SupportedIntentTypes,
	}

	// 初始化核心组件
	if err := recognizer.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize intent recognizer components: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"name":              config.Name,
		"version":           config.Version,
		"supported_intents": len(SupportedIntentTypes),
	}).Info("Next-Gen Intent Recognizer initialized successfully")

	return recognizer, nil
}

// RecognizeIntent 识别意图
func (r *NextGenIntentRecognizer) RecognizeIntent(ctx context.Context, req *IntentRecognitionRequest) (*IntentRecognitionResponse, error) {
	start := time.Now()
	
	r.logger.WithFields(logrus.Fields{
		"request_id":  req.ID,
		"user_input":  req.UserInput,
		"input_type":  req.InputType,
		"session_id":  req.SessionID,
		"user_id":     req.UserID,
	}).Info("Next-Gen Intent Recognition started")

	// 创建处理指标
	metrics := &ProcessingMetrics{}

	// 1. 多模态输入处理
	multimodalStart := time.Now()
	processedInput, err := r.multimodalProcessor.ProcessInput(ctx, req.UserInput, req.InputType)
	if err != nil {
		return nil, fmt.Errorf("multimodal processing failed: %w", err)
	}
	metrics.MultimodalTime = time.Since(multimodalStart)

	// 2. 语义解析
	semanticStart := time.Now()
	semanticResult, err := r.semanticParser.Parse(ctx, processedInput)
	if err != nil {
		return nil, fmt.Errorf("semantic parsing failed: %w", err)
	}
	metrics.SemanticParsingTime = time.Since(semanticStart)

	// 3. 实体识别
	entityStart := time.Now()
	entities, err := r.entityRecognizer.RecognizeEntities(ctx, processedInput, semanticResult)
	if err != nil {
		return nil, fmt.Errorf("entity recognition failed: %w", err)
	}
	metrics.EntityExtractionTime = time.Since(entityStart)

	// 4. 关系抽取
	relations, err := r.relationExtractor.ExtractRelations(ctx, entities, semanticResult)
	if err != nil {
		r.logger.WithError(err).Warn("Relation extraction failed, continuing without relations")
		relations = []*ExtractedRelation{}
	}

	// 5. 意图分类
	classificationStart := time.Now()
	primaryIntent, alternativeIntents, err := r.classifyIntent(ctx, processedInput, semanticResult, entities)
	if err != nil {
		return nil, fmt.Errorf("intent classification failed: %w", err)
	}
	metrics.IntentClassificationTime = time.Since(classificationStart)

	// 6. 意图链推理
	intentChain, err := r.intentChainReasoner.ReasonChain(ctx, primaryIntent, req.Context)
	if err != nil {
		r.logger.WithError(err).Warn("Intent chain reasoning failed, using single intent")
		intentChain = []*IntentChainNode{{Intent: primaryIntent, Sequence: 1, Probability: 1.0}}
	}

	// 7. 参数提取和验证
	parameters, missingParams, err := r.parameterExtractor.ExtractParameters(ctx, primaryIntent, entities, semanticResult)
	if err != nil {
		r.logger.WithError(err).Warn("Parameter extraction failed")
		parameters = make(map[string]interface{})
	}
	primaryIntent.Parameters = parameters
	primaryIntent.MissingParams = missingParams

	// 8. 置信度评估
	confidenceStart := time.Now()
	confidence, err := r.confidenceAssessor.AssessConfidence(ctx, primaryIntent, semanticResult, entities)
	if err != nil {
		r.logger.WithError(err).Warn("Confidence assessment failed, using default")
		confidence = 0.5
	}
	primaryIntent.Confidence = confidence
	metrics.ConfidenceAssessmentTime = time.Since(confidenceStart)

	// 9. 上下文更新
	contextStart := time.Now()
	contextUpdates, err := r.contextManager.UpdateContext(ctx, req.SessionID, primaryIntent, entities)
	if err != nil {
		r.logger.WithError(err).Warn("Context update failed")
		contextUpdates = &ContextUpdates{}
	}
	metrics.ContextProcessingTime = time.Since(contextStart)

	// 10. 生成推荐和澄清
	recommendations := r.generateRecommendations(primaryIntent, alternativeIntents)
	clarifications := r.generateClarifications(primaryIntent, missingParams)

	// 计算总处理时间
	metrics.TotalTime = time.Since(start)

	// 构建响应
	response := &IntentRecognitionResponse{
		ID:                     req.ID,
		PrimaryIntent:          primaryIntent,
		AlternativeIntents:     alternativeIntents,
		IntentChain:           intentChain,
		ExtractedEntities:     entities,
		ExtractedRelations:    relations,
		ContextUpdates:        contextUpdates,
		ProcessingMetrics:     metrics,
		Recommendations:       recommendations,
		RequiredClarifications: clarifications,
	}

	// 更新统计信息
	r.updateStats(metrics.TotalTime, confidence >= r.config.ConfidenceThreshold)

	r.logger.WithFields(logrus.Fields{
		"request_id":       req.ID,
		"primary_intent":   primaryIntent.Type,
		"confidence":       confidence,
		"processing_time":  metrics.TotalTime,
		"entities_count":   len(entities),
		"relations_count":  len(relations),
	}).Info("Next-Gen Intent Recognition completed")

	return response, nil
}

// initializeComponents 初始化核心组件
func (r *NextGenIntentRecognizer) initializeComponents() error {
	var err error

	// 初始化多模态处理器
	r.multimodalProcessor, err = NewMultimodalProcessor(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize multimodal processor: %w", err)
	}

	// 初始化上下文管理器
	r.contextManager, err = NewAdvancedContextManager(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize context manager: %w", err)
	}

	// 初始化意图链推理器
	r.intentChainReasoner, err = NewIntentChainReasoner(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize intent chain reasoner: %w", err)
	}

	// 初始化参数提取器
	r.parameterExtractor, err = NewSmartParameterExtractor(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize parameter extractor: %w", err)
	}

	// 初始化语义解析器
	r.semanticParser, err = NewSemanticParsingEngine(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize semantic parser: %w", err)
	}

	// 初始化实体识别器
	r.entityRecognizer, err = NewNamedEntityRecognizer(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize entity recognizer: %w", err)
	}

	// 初始化关系抽取器
	r.relationExtractor, err = NewRelationExtractionEngine(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize relation extractor: %w", err)
	}

	// 初始化置信度评估器
	r.confidenceAssessor, err = NewConfidenceAssessmentEngine(r.config, r.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize confidence assessor: %w", err)
	}

	return nil
}

// classifyIntent 分类意图
func (r *NextGenIntentRecognizer) classifyIntent(ctx context.Context, input string, semantic interface{}, entities []*ExtractedEntity) (*RecognizedIntent, []*RecognizedIntent, error) {
	// 这里实现复杂的意图分类逻辑
	// 包括基于规则、机器学习模型、深度学习等多种方法的融合

	// 简化实现：基于关键词匹配
	input = strings.ToLower(input)
	
	var candidates []*RecognizedIntent
	
	for intentType, definition := range r.supportedIntents {
		score := r.calculateIntentScore(input, definition, entities)
		if score > 0.1 {
			intent := &RecognizedIntent{
				Type:            intentType,
				Category:        definition.Category,
				Subcategory:     definition.Subcategory,
				Confidence:      score,
				Parameters:      make(map[string]interface{}),
				RequiredParams:  definition.RequiredEntities,
				OptionalParams:  definition.OptionalEntities,
				RiskLevel:       definition.RiskLevel,
				ComplexityLevel: definition.ComplexityLevel,
				Dependencies:    definition.Dependencies,
			}
			candidates = append(candidates, intent)
		}
	}

	if len(candidates) == 0 {
		// 返回默认的通用对话意图
		return &RecognizedIntent{
			Type:        "general_chat",
			Category:    "general",
			Subcategory: "chat",
			Confidence:  0.5,
			Parameters:  map[string]interface{}{"original_input": input},
			RiskLevel:   "none",
			ComplexityLevel: "low",
		}, []*RecognizedIntent{}, nil
	}

	// 按置信度排序
	for i := 0; i < len(candidates)-1; i++ {
		for j := i + 1; j < len(candidates); j++ {
			if candidates[i].Confidence < candidates[j].Confidence {
				candidates[i], candidates[j] = candidates[j], candidates[i]
			}
		}
	}

	primary := candidates[0]
	alternatives := candidates[1:]
	if len(alternatives) > 5 {
		alternatives = alternatives[:5] // 最多返回5个备选意图
	}

	return primary, alternatives, nil
}

// calculateIntentScore 计算意图得分
func (r *NextGenIntentRecognizer) calculateIntentScore(input string, definition *IntentDefinition, entities []*ExtractedEntity) float64 {
	score := 0.0
	
	// 关键词匹配
	for _, keyword := range definition.Keywords {
		if strings.Contains(input, strings.ToLower(keyword)) {
			score += 0.3
		}
	}
	
	// 实体匹配
	for _, entity := range entities {
		for _, requiredEntity := range definition.RequiredEntities {
			if entity.Type == requiredEntity {
				score += 0.4
			}
		}
		for _, optionalEntity := range definition.OptionalEntities {
			if entity.Type == optionalEntity {
				score += 0.2
			}
		}
	}
	
	// 限制最大得分
	if score > 1.0 {
		score = 1.0
	}
	
	return score
}

// generateRecommendations 生成推荐
func (r *NextGenIntentRecognizer) generateRecommendations(primary *RecognizedIntent, alternatives []*RecognizedIntent) []*IntentRecommendation {
	var recommendations []*IntentRecommendation
	
	// 基于主要意图生成推荐
	if primary.Confidence < 0.8 && len(alternatives) > 0 {
		for _, alt := range alternatives {
			if alt.Confidence > 0.5 {
				rec := &IntentRecommendation{
					Type:        "alternative_intent",
					Description: fmt.Sprintf("您是否想要执行 %s 操作？", alt.Type),
					Confidence:  alt.Confidence,
					Reason:      "基于输入内容的替代意图建议",
					Action:      alt.Type,
				}
				recommendations = append(recommendations, rec)
			}
		}
	}
	
	return recommendations
}

// generateClarifications 生成澄清请求
func (r *NextGenIntentRecognizer) generateClarifications(intent *RecognizedIntent, missingParams []string) []*Clarification {
	var clarifications []*Clarification
	
	for _, param := range missingParams {
		clarification := &Clarification{
			Type:     "missing_parameter",
			Question: fmt.Sprintf("请提供 %s 参数", param),
			Required: true,
			Context:  intent.Type,
		}
		clarifications = append(clarifications, clarification)
	}
	
	return clarifications
}

// updateStats 更新统计信息
func (r *NextGenIntentRecognizer) updateStats(processingTime time.Duration, success bool) {
	r.mutex.Lock()
	defer r.mutex.Unlock()

	r.processedCount++

	// 更新平均处理时间
	if r.avgProcessTime == 0 {
		r.avgProcessTime = processingTime
	} else {
		r.avgProcessTime = (r.avgProcessTime + processingTime) / 2
	}

	// 更新准确率
	if success {
		r.accuracyRate = (r.accuracyRate*float64(r.processedCount-1) + 1.0) / float64(r.processedCount)
	} else {
		r.accuracyRate = (r.accuracyRate * float64(r.processedCount-1)) / float64(r.processedCount)
	}
}

// getDefaultIntentEngineConfig 获取默认配置
func getDefaultIntentEngineConfig() *IntentEngineConfig {
	return &IntentEngineConfig{
		Name:                  "NextGen-Intent-Engine",
		Version:               "2.0.0",
		MaxConcurrentRequests: 1000,
		ProcessingTimeout:     10 * time.Second,
		ConfidenceThreshold:   0.7,
		MultimodalEnabled:     true,
		ContextWindowSize:     10,
		IntentChainMaxDepth:   5,
		SelfLearningEnabled:   true,
		CacheEnabled:          true,
	}
}
